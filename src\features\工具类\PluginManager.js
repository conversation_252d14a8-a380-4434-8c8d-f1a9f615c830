/**
 * 插件管理器 - 实现动态插件加载和管理
 * 支持运行时插件安装、卸载和热更新，实现真正的模块化架构
 */
class PluginManager {
    constructor(viewer) {
        this.viewer = viewer;
        this.plugins = new Map();
        this.pluginDependencies = new Map();
        this.loadedModules = new Map();
        this.moduleCache = new Map();
        
        // 插件生命周期钩子
        this.hooks = {
            beforeLoad: new Set(),
            afterLoad: new Set(),
            beforeUnload: new Set(),
            afterUnload: new Set(),
            onError: new Set()
        };
        
        // 插件API
        this.pluginAPI = this.createPluginAPI();
        
        // 动态加载配置
        this.loadConfig = {
            timeout: 30000,      // 30秒超时
            retries: 3,          // 重试3次
            cacheEnabled: true,  // 启用缓存
            lazyLoad: true       // 懒加载
        };
        
        this.initializePluginSystem();
        console.log('🔌 PluginManager 插件管理器初始化完成');
    }
    
    /**
     * 初始化插件系统
     */
    initializePluginSystem() {
        // 创建插件加载器
        this.moduleLoader = new ModuleLoader(this.loadConfig);
        
        // 设置依赖注入器
        this.dependencyInjector = new DependencyInjector();
        
        // 注册核心服务
        this.registerCoreServices();
        
        // 设置插件安全沙箱
        this.setupPluginSandbox();
        
        // 扫描并自动加载插件（可选）
        // this.autoDiscoverPlugins(); // 注释掉以禁用自动发现
    }
    
    /**
     * 创建插件API
     */
    createPluginAPI() {
        const self = this;
        
        return {
            // 核心服务访问
            viewer: this.viewer,
            eventBus: window.EventBus,
            buttonConfig: window.ButtonConfig,
            panelManager: null, // 将在需要时注入
            
            // 工具方法
            utils: {
                generateId: () => `plugin_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                formatCoordinate: (coord, precision = 6) => coord.toFixed(precision),
                calculateDistance: this.calculateDistance.bind(this),
                parseGeoJSON: this.parseGeoJSON.bind(this)
            },
            
            // 事件系统
            on: (event, handler) => window.EventBus.on(event, handler),
            emit: (event, data) => window.EventBus.emit(event, data),
            off: (event, handler) => window.EventBus.off(event, handler),
            
            // UI组件
            ui: {
                createButton: this.createPluginButton.bind(this),
                createPanel: this.createPluginPanel.bind(this),
                showNotification: this.showNotification.bind(this),
                createModal: this.createModal.bind(this)
            },
            
            // 存储API
            storage: {
                set: (key, value) => localStorage.setItem(`plugin_${key}`, JSON.stringify(value)),
                get: (key) => {
                    const item = localStorage.getItem(`plugin_${key}`);
                    return item ? JSON.parse(item) : null;
                },
                remove: (key) => localStorage.removeItem(`plugin_${key}`)
            },
            
            // 网络请求
            http: {
                get: this.makeHttpRequest.bind(this, 'GET'),
                post: this.makeHttpRequest.bind(this, 'POST'),
                put: this.makeHttpRequest.bind(this, 'PUT'),
                delete: this.makeHttpRequest.bind(this, 'DELETE')
            },
            
            // 插件间通信
            plugin: {
                invoke: (pluginId, method, ...args) => self.invokePluginMethod(pluginId, method, ...args),
                sendMessage: (targetPluginId, message) => self.sendPluginMessage(targetPluginId, message),
                subscribeToPlugin: (pluginId, event, handler) => self.subscribeToPluginEvent(pluginId, event, handler)
            }
        };
    }
    
    /**
     * 注册核心服务
     */
    registerCoreServices() {
        this.dependencyInjector.register('viewer', this.viewer);
        this.dependencyInjector.register('eventBus', window.EventBus);
        this.dependencyInjector.register('buttonConfig', window.ButtonConfig);
        this.dependencyInjector.register('pluginAPI', this.pluginAPI);
        
        console.log('🔧 核心服务已注册');
    }
    
    /**
     * 设置插件安全沙箱
     */
    setupPluginSandbox() {
        // 创建受限的全局对象
        this.sandboxGlobals = {
            console: {
                log: (...args) => console.log('[Plugin]', ...args),
                warn: (...args) => console.warn('[Plugin]', ...args),
                error: (...args) => console.error('[Plugin]', ...args),
                info: (...args) => console.info('[Plugin]', ...args)
            },
            setTimeout: setTimeout,
            setInterval: setInterval,
            clearTimeout: clearTimeout,
            clearInterval: clearInterval,
            Date: Date,
            Math: Math,
            JSON: JSON,
            Promise: Promise,
            fetch: fetch,
            
            // 禁止的全局对象
            window: undefined,
            document: undefined,
            localStorage: undefined,
            sessionStorage: undefined
        };
        
        console.log('🛡️ 插件安全沙箱已设置');
    }
    
    /**
     * 自动发现插件
     */
    async autoDiscoverPlugins() {
        const pluginPaths = [
            'src/plugins/',
            'src/features/plugins/',
            'plugins/'
        ];
        
        for (const path of pluginPaths) {
            try {
                await this.scanPluginsInPath(path);
            } catch (error) {
                console.warn(`扫描插件路径 ${path} 失败:`, error);
            }
        }
    }
    
    /**
     * 扫描指定路径的插件
     */
    async scanPluginsInPath(path) {
        try {
            // 尝试加载插件清单文件
            const manifestUrl = `${path}plugins-manifest.json`;
            const response = await fetch(manifestUrl);
            
            if (response.ok) {
                const manifest = await response.json();
                console.log(`📋 发现插件清单: ${path}`, manifest);
                
                // 根据清单加载插件
                for (const pluginInfo of manifest.plugins) {
                    if (pluginInfo.autoLoad !== false) {
                        await this.loadPlugin(pluginInfo.id, `${path}${pluginInfo.entry}`);
                    }
                }
            }
        } catch (error) {
            console.warn(`加载插件清单失败 ${path}:`, error);
        }
    }
    
    /**
     * 加载插件
     */
    async loadPlugin(pluginId, scriptPath, options = {}) {
        if (this.plugins.has(pluginId)) {
            console.warn(`插件 ${pluginId} 已存在`);
            return this.plugins.get(pluginId);
        }
        
        try {
            console.log(`🔄 开始加载插件: ${pluginId}`);
            
            // 执行beforeLoad钩子
            await this.executeHooks('beforeLoad', { pluginId, scriptPath, options });
            
            // 动态加载插件模块
            const pluginModule = await this.moduleLoader.load(scriptPath);
            
            // 验证插件结构
            this.validatePlugin(pluginModule, pluginId);
            
            // 创建插件实例
            const pluginInstance = await this.createPluginInstance(pluginModule, pluginId, options);
            
            // 注册插件
            this.plugins.set(pluginId, {
                id: pluginId,
                module: pluginModule,
                instance: pluginInstance,
                path: scriptPath,
                options: options,
                loadTime: Date.now(),
                status: 'loaded'
            });
            
            // 执行afterLoad钩子
            await this.executeHooks('afterLoad', { pluginId, instance: pluginInstance });
            
            console.log(`✅ 插件加载成功: ${pluginId}`);
            
            // 触发插件加载事件
            if (window.EventBus) {
                window.EventBus.emit('plugin:loaded', {
                    pluginId,
                    instance: pluginInstance
                });
            }
            
            return pluginInstance;
            
        } catch (error) {
            console.error(`❌ 插件加载失败 ${pluginId}:`, error);
            
            // 执行错误钩子
            await this.executeHooks('onError', { pluginId, error, action: 'load' });
            
            throw error;
        }
    }
    
    /**
     * 验证插件结构
     */
    validatePlugin(pluginModule, pluginId) {
        if (!pluginModule || typeof pluginModule !== 'object') {
            throw new Error(`插件 ${pluginId} 必须导出一个对象`);
        }
        
        if (!pluginModule.name || typeof pluginModule.name !== 'string') {
            throw new Error(`插件 ${pluginId} 必须有一个name属性`);
        }
        
        if (!pluginModule.version || typeof pluginModule.version !== 'string') {
            throw new Error(`插件 ${pluginId} 必须有一个version属性`);
        }
        
        if (!pluginModule.init || typeof pluginModule.init !== 'function') {
            throw new Error(`插件 ${pluginId} 必须有一个init方法`);
        }
        
        // 检查依赖
        if (pluginModule.dependencies && Array.isArray(pluginModule.dependencies)) {
            for (const dep of pluginModule.dependencies) {
                if (!this.checkDependency(dep)) {
                    throw new Error(`插件 ${pluginId} 的依赖 ${dep} 不满足`);
                }
            }
        }
    }
    
    /**
     * 检查依赖
     */
    checkDependency(dependency) {
        if (typeof dependency === 'string') {
            // 简单的插件ID依赖
            return this.plugins.has(dependency);
        } else if (dependency && dependency.plugin) {
            // 带版本的依赖检查
            const plugin = this.plugins.get(dependency.plugin);
            if (!plugin) return false;
            
            if (dependency.version) {
                return this.compareVersions(plugin.module.version, dependency.version) >= 0;
            }
            
            return true;
        }
        
        return false;
    }
    
    /**
     * 版本比较
     */
    compareVersions(version1, version2) {
        const v1parts = version1.split('.').map(Number);
        const v2parts = version2.split('.').map(Number);
        
        for (let i = 0; i < Math.max(v1parts.length, v2parts.length); i++) {
            const v1part = v1parts[i] || 0;
            const v2part = v2parts[i] || 0;
            
            if (v1part > v2part) return 1;
            if (v1part < v2part) return -1;
        }
        
        return 0;
    }
    
    /**
     * 创建插件实例
     */
    async createPluginInstance(pluginModule, pluginId, options) {
        // 创建插件上下文
        const context = {
            ...this.pluginAPI,
            pluginId: pluginId,
            config: options,
            dependencies: this.dependencyInjector
        };
        
        // 在沙箱中初始化插件
        const instance = await this.initializePluginInSandbox(pluginModule, context);
        
        return instance;
    }
    
    /**
     * 在沙箱中初始化插件
     */
    async initializePluginInSandbox(pluginModule, context) {
        try {
            // 创建沙箱环境
            const sandbox = {
                ...this.sandboxGlobals,
                pluginAPI: context,
                console: context.console || this.sandboxGlobals.console
            };
            
            // 在沙箱中执行初始化
            const initResult = await pluginModule.init.call(sandbox, context);
            
            // 返回插件实例
            return {
                ...pluginModule,
                initResult: initResult,
                context: context,
                sandbox: sandbox
            };
            
        } catch (error) {
            console.error('插件沙箱初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 卸载插件
     */
    async unloadPlugin(pluginId) {
        const plugin = this.plugins.get(pluginId);
        if (!plugin) {
            console.warn(`插件 ${pluginId} 不存在`);
            return;
        }
        
        try {
            console.log(`🔄 开始卸载插件: ${pluginId}`);
            
            // 执行beforeUnload钩子
            await this.executeHooks('beforeUnload', { pluginId, plugin });
            
            // 调用插件的销毁方法
            if (plugin.instance && typeof plugin.instance.destroy === 'function') {
                await plugin.instance.destroy();
            }
            
            // 清理插件注册的组件
            this.cleanupPluginComponents(pluginId);
            
            // 从注册表中移除
            this.plugins.delete(pluginId);
            
            // 执行afterUnload钩子
            await this.executeHooks('afterUnload', { pluginId });
            
            console.log(`✅ 插件卸载成功: ${pluginId}`);
            
            // 触发插件卸载事件
            if (window.EventBus) {
                window.EventBus.emit('plugin:unloaded', { pluginId });
            }
            
        } catch (error) {
            console.error(`❌ 插件卸载失败 ${pluginId}:`, error);
            await this.executeHooks('onError', { pluginId, error, action: 'unload' });
            throw error;
        }
    }
    
    /**
     * 清理插件组件
     */
    cleanupPluginComponents(pluginId) {
        // 清理插件创建的按钮
        const buttonsToRemove = [];
        if (window.ButtonConfig) {
            const configs = window.ButtonConfig.getAllConfigs();
            Object.keys(configs).forEach(configId => {
                if (configId.startsWith(`${pluginId}_`)) {
                    buttonsToRemove.push(configId);
                }
            });
        }
        
        buttonsToRemove.forEach(buttonId => {
            if (window.enhancedToolbarManager) {
                window.enhancedToolbarManager.removeTool(buttonId);
            }
        });
        
        // 清理插件的事件监听器
        if (window.EventBus) {
            // 清理以插件ID为命名空间的事件
            window.EventBus.removeNamespace(`plugin:${pluginId}`);
        }
        
        console.log(`🗑️ 插件 ${pluginId} 的组件已清理`);
    }
    
    /**
     * 重载插件
     */
    async reloadPlugin(pluginId) {
        const plugin = this.plugins.get(pluginId);
        if (!plugin) {
            throw new Error(`插件 ${pluginId} 不存在`);
        }
        
        const { path, options } = plugin;
        
        // 先卸载
        await this.unloadPlugin(pluginId);
        
        // 清除模块缓存
        this.moduleCache.delete(path);
        
        // 重新加载
        return await this.loadPlugin(pluginId, path, options);
    }
    
    /**
     * 执行钩子
     */
    async executeHooks(hookName, data) {
        const hooks = this.hooks[hookName];
        if (!hooks || hooks.size === 0) return;
        
        const promises = Array.from(hooks).map(hook => {
            try {
                return hook(data);
            } catch (error) {
                console.error(`钩子 ${hookName} 执行失败:`, error);
                return null;
            }
        });
        
        await Promise.allSettled(promises);
    }
    
    /**
     * 添加钩子
     */
    addHook(hookName, handler) {
        if (!this.hooks[hookName]) {
            this.hooks[hookName] = new Set();
        }
        
        this.hooks[hookName].add(handler);
    }
    
    /**
     * 移除钩子
     */
    removeHook(hookName, handler) {
        if (this.hooks[hookName]) {
            this.hooks[hookName].delete(handler);
        }
    }
    
    /**
     * 创建插件按钮
     */
    createPluginButton(config) {
        const pluginButtonId = `plugin_${config.pluginId}_${config.id}`;
        
        const buttonConfig = {
            id: pluginButtonId,
            name: config.name,
            tooltip: config.tooltip,
            category: config.category || 'plugin',
            icon: config.icon,
            shortcuts: config.shortcuts || [],
            panel: config.panel,
            enabled: true,
            pluginId: config.pluginId
        };
        
        // 添加到按钮配置
        if (window.ButtonConfig) {
            window.ButtonConfig.addConfig(pluginButtonId, buttonConfig);
        }
        
        // 添加到工具栏管理器
        if (window.enhancedToolbarManager) {
            window.enhancedToolbarManager.addTool(buttonConfig, config.toolClass);
        }
        
        return pluginButtonId;
    }
    
    /**
     * 创建插件面板
     */
    createPluginPanel(config) {
        if (!window.PanelManager) return null;
        
        const panelId = `plugin_panel_${config.pluginId}_${Date.now()}`;
        
        return window.PanelManager.createPanel({
            id: panelId,
            title: config.title,
            width: config.width || 300,
            height: config.height || 400,
            content: config.content || '',
            template: config.template || 'default-panel',
            draggable: config.draggable !== false,
            resizable: config.resizable !== false
        });
    }
    
    /**
     * 显示通知
     */
    showNotification(message, type = 'info', duration = 3000) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `plugin-notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#f44336' : type === 'warning' ? '#ff9800' : '#4caf50'};
            color: white;
            padding: 12px 20px;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        document.body.appendChild(notification);
        
        // 自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, duration);
    }
    
    /**
     * 创建模态框
     */
    createModal(config) {
        const modal = document.createElement('div');
        modal.className = 'plugin-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        `;
        
        const modalContent = document.createElement('div');
        modalContent.className = 'plugin-modal-content';
        modalContent.style.cssText = `
            background: white;
            border-radius: 8px;
            padding: 20px;
            max-width: ${config.maxWidth || '500px'};
            max-height: ${config.maxHeight || '80vh'};
            overflow: auto;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        `;
        
        if (config.title) {
            const title = document.createElement('h3');
            title.textContent = config.title;
            title.style.marginTop = '0';
            modalContent.appendChild(title);
        }
        
        if (config.content) {
            const content = document.createElement('div');
            content.innerHTML = config.content;
            modalContent.appendChild(content);
        }
        
        modal.appendChild(modalContent);
        document.body.appendChild(modal);
        
        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
        
        return modal;
    }
    
    /**
     * HTTP请求
     */
    async makeHttpRequest(method, url, data = null, headers = {}) {
        const config = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                ...headers
            }
        };
        
        if (data && (method === 'POST' || method === 'PUT')) {
            config.body = JSON.stringify(data);
        }
        
        const response = await fetch(url, config);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        } else {
            return await response.text();
        }
    }
    
    /**
     * 调用插件方法
     */
    async invokePluginMethod(pluginId, method, ...args) {
        const plugin = this.plugins.get(pluginId);
        if (!plugin) {
            throw new Error(`插件 ${pluginId} 不存在`);
        }
        
        if (!plugin.instance || typeof plugin.instance[method] !== 'function') {
            throw new Error(`插件 ${pluginId} 没有方法 ${method}`);
        }
        
        return await plugin.instance[method](...args);
    }
    
    /**
     * 发送插件消息
     */
    sendPluginMessage(targetPluginId, message) {
        if (window.EventBus) {
            window.EventBus.emit(`plugin:${targetPluginId}:message`, message);
        }
    }
    
    /**
     * 订阅插件事件
     */
    subscribeToPluginEvent(pluginId, event, handler) {
        if (window.EventBus) {
            return window.EventBus.on(`plugin:${pluginId}:${event}`, handler);
        }
    }
    
    /**
     * 计算距离
     */
    calculateDistance(point1, point2) {
        const R = 6371000; // 地球半径（米）
        const lat1 = point1.latitude * Math.PI / 180;
        const lat2 = point2.latitude * Math.PI / 180;
        const deltaLat = (point2.latitude - point1.latitude) * Math.PI / 180;
        const deltaLon = (point2.longitude - point1.longitude) * Math.PI / 180;
        
        const a = Math.sin(deltaLat/2) * Math.sin(deltaLat/2) +
                Math.cos(lat1) * Math.cos(lat2) *
                Math.sin(deltaLon/2) * Math.sin(deltaLon/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        
        return R * c;
    }
    
    /**
     * 解析GeoJSON
     */
    parseGeoJSON(geojson) {
        try {
            if (typeof geojson === 'string') {
                geojson = JSON.parse(geojson);
            }
            
            // 验证GeoJSON格式
            if (!geojson.type || !['Feature', 'FeatureCollection', 'Point', 'LineString', 'Polygon'].includes(geojson.type)) {
                throw new Error('无效的GeoJSON格式');
            }
            
            return geojson;
        } catch (error) {
            throw new Error(`GeoJSON解析失败: ${error.message}`);
        }
    }
    
    /**
     * 获取所有插件信息
     */
    getPluginList() {
        const plugins = [];
        
        this.plugins.forEach((plugin, id) => {
            plugins.push({
                id: id,
                name: plugin.module.name,
                version: plugin.module.version,
                description: plugin.module.description,
                author: plugin.module.author,
                status: plugin.status,
                loadTime: plugin.loadTime
            });
        });
        
        return plugins;
    }
    
    /**
     * 获取插件详细信息
     */
    getPluginInfo(pluginId) {
        const plugin = this.plugins.get(pluginId);
        if (!plugin) return null;
        
        return {
            id: pluginId,
            name: plugin.module.name,
            version: plugin.module.version,
            description: plugin.module.description,
            author: plugin.module.author,
            dependencies: plugin.module.dependencies,
            status: plugin.status,
            loadTime: plugin.loadTime,
            path: plugin.path,
            options: plugin.options
        };
    }
    
    /**
     * 销毁插件管理器
     */
    destroy() {
        // 卸载所有插件
        const pluginIds = Array.from(this.plugins.keys());
        pluginIds.forEach(pluginId => {
            this.unloadPlugin(pluginId).catch(console.error);
        });
        
        // 清理资源
        this.plugins.clear();
        this.pluginDependencies.clear();
        this.loadedModules.clear();
        this.moduleCache.clear();
        
        // 清理钩子
        Object.values(this.hooks).forEach(hookSet => hookSet.clear());
        
        console.log('🗑️ PluginManager 已销毁');
    }
}

/**
 * 模块加载器 - 负责动态加载JavaScript模块
 */
class ModuleLoader {
    constructor(config) {
        this.config = config;
        this.cache = new Map();
    }
    
    async load(scriptPath) {
        // 检查缓存
        if (this.config.cacheEnabled && this.cache.has(scriptPath)) {
            return this.cache.get(scriptPath);
        }
        
        let retries = this.config.retries;
        
        while (retries > 0) {
            try {
                const module = await this.loadScript(scriptPath);
                
                // 缓存模块
                if (this.config.cacheEnabled) {
                    this.cache.set(scriptPath, module);
                }
                
                return module;
                
            } catch (error) {
                retries--;
                if (retries === 0) {
                    throw error;
                }
                
                console.warn(`模块加载失败，还剩 ${retries} 次重试:`, error);
                await this.delay(1000); // 等待1秒后重试
            }
        }
    }
    
    async loadScript(scriptPath) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = scriptPath;
            script.type = 'module';
            
            const timeout = setTimeout(() => {
                reject(new Error(`模块加载超时: ${scriptPath}`));
            }, this.config.timeout);
            
            script.onload = () => {
                clearTimeout(timeout);
                
                // 尝试从全局对象获取模块
                const moduleName = this.extractModuleName(scriptPath);
                const module = window[moduleName] || {};
                
                resolve(module);
            };
            
            script.onerror = () => {
                clearTimeout(timeout);
                reject(new Error(`模块加载失败: ${scriptPath}`));
            };
            
            document.head.appendChild(script);
        });
    }
    
    extractModuleName(scriptPath) {
        const filename = scriptPath.split('/').pop();
        return filename.replace('.js', '').replace(/[^a-zA-Z0-9]/g, '');
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

/**
 * 依赖注入器
 */
class DependencyInjector {
    constructor() {
        this.services = new Map();
    }
    
    register(name, service) {
        this.services.set(name, service);
    }
    
    get(name) {
        return this.services.get(name);
    }
    
    has(name) {
        return this.services.has(name);
    }
    
    inject(target) {
        if (target.dependencies && Array.isArray(target.dependencies)) {
            const injected = {};
            
            target.dependencies.forEach(dep => {
                if (this.has(dep)) {
                    injected[dep] = this.get(dep);
                }
            });
            
            return injected;
        }
        
        return {};
    }
}

// 创建全局插件管理器实例
if (typeof window !== 'undefined') {
    window.PluginManager = PluginManager;
}

console.log('🔌 PluginManager 插件管理器已加载完成');