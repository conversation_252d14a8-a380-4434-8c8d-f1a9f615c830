/**
 * 系统集成器 - 整合所有新的架构组件
 * 负责协调WorkerManager、MemoryManager、PluginManager和现有系统
 */
class SystemIntegrator {
    constructor(viewer) {
        this.viewer = viewer;
        this.components = {
            workerManager: null,
            memoryManager: null,
            pluginManager: null,
            performanceManager: null,
            toolbarManager: null
        };
        
        this.systemStatus = {
            isInitialized: false,
            startTime: Date.now(),
            performanceLevel: 'balanced',
            activeFeatures: new Set()
        };
        
        this.integrationConfig = {
            enableWorkers: true,
            enableMemoryManagement: true,
            enablePlugins: true,
            enablePerformanceOptimization: true,
            autoOptimize: true
        };
        
        console.log('🔗 SystemIntegrator 系统集成器初始化开始...');
    }
    
    /**
     * 初始化完整系统
     */
    async initializeSystem() {
        try {
            console.log('🚀 开始系统集成初始化...');
            
            // 1. 初始化内存管理器
            await this.initializeMemoryManager();
            
            // 2. 初始化Worker管理器
            await this.initializeWorkerManager();
            
            // 3. 初始化插件管理器
            await this.initializePluginManager();
            
            // 4. 集成现有的性能管理器
            await this.integratePerformanceManager();
            
            // 5. 集成工具栏管理器
            await this.integrateToolbarManager();
            
            // 6. 设置组件间协调
            this.setupComponentCoordination();
            
            // 7. 启动系统监控
            this.startSystemMonitoring();
            
            // 8. 注册系统API
            this.registerSystemAPI();
            
            this.systemStatus.isInitialized = true;
            console.log('✅ 系统集成初始化完成!');
            
            // 触发系统就绪事件
            this.emitSystemReady();
            
            return this.getSystemInfo();
            
        } catch (error) {
            console.error('❌ 系统集成初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 初始化内存管理器
     */
    async initializeMemoryManager() {
        if (!this.integrationConfig.enableMemoryManagement) return;
        
        try {
            this.components.memoryManager = new MemoryManager(this.viewer);
            
            // 设置内存事件监听
            if (window.EventBus) {
                window.EventBus.on('system:memory:leak-warning', (data) => {
                    this.handleMemoryLeak(data);
                });
                
                window.EventBus.on('system:memory:status', (data) => {
                    this.updateSystemMetrics('memory', data);
                });
            }
            
            console.log('✅ 内存管理器集成成功');
        } catch (error) {
            console.error('❌ 内存管理器集成失败:', error);
        }
    }
    
    /**
     * 初始化Worker管理器
     */
    async initializeWorkerManager() {
        if (!this.integrationConfig.enableWorkers) return;
        
        try {
            // 检查Worker支持
            if (typeof Worker === 'undefined') {
                console.warn('⚠️ 浏览器不支持Web Workers，跳过Worker管理器初始化');
                return;
            }
            
            this.components.workerManager = window.workerManager || new WorkerManager();
            
            // 等待Worker就绪
            if (typeof this.components.workerManager.getWorkerStatus === 'function') {
                const workerStatus = this.components.workerManager.getWorkerStatus();
                console.log('🔧 Worker状态:', workerStatus);
            }
            
            // 集成Worker到现有工具
            this.integrateWorkersWithTools();
            
            console.log('✅ Worker管理器集成成功');
        } catch (error) {
            console.error('❌ Worker管理器集成失败:', error);
        }
    }
    
    /**
     * 初始化插件管理器
     */
    async initializePluginManager() {
        if (!this.integrationConfig.enablePlugins) return;
        
        try {
            this.components.pluginManager = new PluginManager(this.viewer);
            
            // 设置插件事件监听
            if (window.EventBus) {
                window.EventBus.on('plugin:loaded', (data) => {
                    console.log(`🔌 插件已加载: ${data.pluginId}`);
                    this.systemStatus.activeFeatures.add(`plugin:${data.pluginId}`);
                });
                
                window.EventBus.on('plugin:unloaded', (data) => {
                    console.log(`🔌 插件已卸载: ${data.pluginId}`);
                    this.systemStatus.activeFeatures.delete(`plugin:${data.pluginId}`);
                });
            }
            
            console.log('✅ 插件管理器集成成功');
        } catch (error) {
            console.error('❌ 插件管理器集成失败:', error);
        }
    }
    
    /**
     * 集成现有的性能管理器
     */
    async integratePerformanceManager() {
        try {
            // 如果全局性能管理器不存在，创建一个
            if (!window.performanceManager && window.PerformanceManager) {
                window.performanceManager = new window.PerformanceManager(this.viewer);
            }
            
            this.components.performanceManager = window.performanceManager;
            
            if (this.components.performanceManager) {
                // 连接内存管理器和性能管理器
                if (this.components.memoryManager) {
                    this.setupPerformanceMemoryCoordination();
                }
                
                console.log('✅ 性能管理器集成成功');
            } else {
                console.warn('⚠️ 性能管理器未找到，将跳过性能管理器集成');
            }
        } catch (error) {
            console.error('❌ 性能管理器集成失败:', error);
        }
    }
    
    /**
     * 集成工具栏管理器
     */
    async integrateToolbarManager() {
        try {
            // 优先使用增强版工具栏管理器，否则使用标准版
            this.components.toolbarManager = window.enhancedToolbarManager || window.toolbarManager;
            
            if (this.components.toolbarManager) {
                // 增强工具栏管理器的功能
                this.enhanceToolbarManager();
                
                console.log('✅ 工具栏管理器集成成功');
            } else {
                console.warn('⚠️ 工具栏管理器未找到，将跳过工具栏管理器集成');
            }
        } catch (error) {
            console.error('❌ 工具栏管理器集成失败:', error);
        }
    }
    
    /**
     * 将Worker集成到现有工具
     */
    integrateWorkersWithTools() {
        if (!this.components.workerManager) return;
        
        // 增强地形开挖工具
        this.enhanceTerrainDiggingWithWorker();
        
        // 增强剖面分析工具
        this.enhanceProfileAnalysisWithWorker();
        
        // 增强几何计算
        this.enhanceGeometryCalculationWithWorker();
    }
    
    /**
     * 增强地形开挖工具
     */
    enhanceTerrainDiggingWithWorker() {
        // 重写TerrainDigHandler的体积计算方法
        if (window.TerrainDigHandler) {
            const originalPrototype = window.TerrainDigHandler.prototype;
            
            if (originalPrototype.calculateVolume) {
                originalPrototype.calculateVolumeOriginal = originalPrototype.calculateVolume;
                
                originalPrototype.calculateVolume = async function(points, depth) {
                    try {
                        // 使用Worker进行计算
                        const workerManager = window.systemIntegrator?.components?.workerManager;
                        if (!workerManager || typeof workerManager.calculateVolume !== 'function') {
                            throw new Error('WorkerManager不可用');
                        }
                        const result = await workerManager.calculateVolume(points, depth);
                        
                        return result.data;
                    } catch (error) {
                        console.warn('Worker计算失败，回退到原始方法:', error);
                        return this.calculateVolumeOriginal(points, depth);
                    }
                };
            }
        }
    }
    
    /**
     * 增强剖面分析工具
     */
    enhanceProfileAnalysisWithWorker() {
        if (window.ProfileAnalysis) {
            const originalPrototype = window.ProfileAnalysis.prototype;
            
            if (originalPrototype.calculateProfile) {
                originalPrototype.calculateProfileOriginal = originalPrototype.calculateProfile;
                
                originalPrototype.calculateProfile = async function() {
                    try {
                        const start = this.profilePoints[0];
                        const end = this.profilePoints[1];
                        
                        // 使用Worker进行计算
                        const workerManager = window.systemIntegrator?.components?.workerManager;
                        if (!workerManager || typeof workerManager.calculateProfile !== 'function') {
                            throw new Error('WorkerManager不可用');
                        }
                        const result = await workerManager.calculateProfile(
                            { longitude: start[0], latitude: start[1], height: start[2] },
                            { longitude: end[0], latitude: end[1], height: end[2] }
                        );
                        
                        this.updateChart(result.data.profilePoints.map(p => [p.distance / 1000, p.height]));
                        
                    } catch (error) {
                        console.warn('Worker计算失败，回退到原始方法:', error);
                        this.calculateProfileOriginal();
                    }
                };
            }
        }
    }
    
    /**
     * 增强几何计算
     */
    enhanceGeometryCalculationWithWorker() {
        // 创建全局几何计算API
        window.GeometryAPI = {
            simplify: async (coordinates, tolerance) => {
                return await this.components.workerManager.processGeometry(
                    coordinates, 'SIMPLIFY', { tolerance }
                );
            },
            
            buffer: async (coordinates, distance) => {
                return await this.components.workerManager.processGeometry(
                    coordinates, 'BUFFER', { distance }
                );
            },
            
            intersection: async (coordinates, otherGeometry) => {
                return await this.components.workerManager.processGeometry(
                    coordinates, 'INTERSECTION', { otherGeometry }
                );
            },
            
            convexHull: async (coordinates) => {
                return await this.components.workerManager.processGeometry(
                    coordinates, 'CONVEX_HULL'
                );
            }
        };
    }
    
    /**
     * 设置组件间协调
     */
    setupComponentCoordination() {
        // 设置内存和性能协调
        this.setupPerformanceMemoryCoordination();
        
        // 设置插件和工具栏协调
        this.setupPluginToolbarCoordination();
        
        // 设置Worker和内存协调
        this.setupWorkerMemoryCoordination();
        
        console.log('🔗 组件间协调已设置');
    }
    
    /**
     * 性能和内存协调
     */
    setupPerformanceMemoryCoordination() {
        if (!this.components.memoryManager || !this.components.performanceManager) return;
        
        // 内存压力时自动降低性能
        if (window.EventBus) {
            window.EventBus.on('system:memory:status', (data) => {
                const memoryUsage = data.usedJSHeapSize;
                const memoryLimit = data.jsHeapSizeLimit;
                const usageRatio = memoryUsage / memoryLimit;
                
                if (usageRatio > 0.8 && this.systemStatus.performanceLevel !== 'performance') {
                    console.log('📉 内存使用过高，自动切换到高性能模式');
                    this.setPerformanceLevel('performance');
                }
            });
        }
    }
    
    /**
     * 插件和工具栏协调
     */
    setupPluginToolbarCoordination() {
        if (!this.components.pluginManager || !this.components.toolbarManager) return;
        
        // 插件加载时自动添加到工具栏
        if (window.EventBus) {
            window.EventBus.on('plugin:loaded', (data) => {
                const plugin = data.instance;
                
                if (plugin.toolbarConfig) {
                    // 自动创建工具栏按钮
                    this.components.pluginManager.createPluginButton({
                        ...plugin.toolbarConfig,
                        pluginId: data.pluginId
                    });
                }
            });
        }
    }
    
    /**
     * Worker和内存协调
     */
    setupWorkerMemoryCoordination() {
        if (!this.components.workerManager || !this.components.memoryManager) return;
        
        // 内存紧张时优先使用Worker
        if (window.EventBus) {
            window.EventBus.on('system:memory:leak-warning', () => {
                console.log('🔄 内存泄漏警告，优先使用Worker处理计算任务');
                
                // 可以在这里设置标志，让工具优先使用Worker
                this.preferWorkerCalculation = true;
            });
        }
    }
    
    /**
     * 启动系统监控
     */
    startSystemMonitoring() {
        // 定期收集系统指标
        setInterval(() => {
            this.collectSystemMetrics();
        }, 60000); // 每分钟收集一次
        
        // 立即执行一次
        this.collectSystemMetrics();
        
        console.log('📊 系统监控已启动');
    }
    
    /**
     * 收集系统指标
     */
    async collectSystemMetrics() {
        const metrics = {
            timestamp: Date.now(),
            uptime: Date.now() - this.systemStatus.startTime,
            performanceLevel: this.systemStatus.performanceLevel,
            activeFeatures: Array.from(this.systemStatus.activeFeatures)
        };
        
        // 收集内存指标
        if (this.components.memoryManager) {
            metrics.memory = this.components.memoryManager.getMemoryStats();
        }
        
        // 收集Worker指标
        if (this.components.workerManager && typeof this.components.workerManager.getWorkerStatus === 'function') {
            metrics.workers = this.components.workerManager.getWorkerStatus();
        }
        
        // 收集插件指标
        if (this.components.pluginManager) {
            metrics.plugins = {
                count: this.components.pluginManager.getPluginList().length,
                list: this.components.pluginManager.getPluginList()
            };
        }
        
        // 收集工具栏指标
        if (this.components.toolbarManager && typeof this.components.toolbarManager.getAllToolStates === 'function') {
            metrics.toolbar = this.components.toolbarManager.getAllToolStates();
        }
        
        // 触发指标事件
        if (window.EventBus) {
            window.EventBus.emit('system:metrics', metrics);
        }
        
        return metrics;
    }
    
    /**
     * 处理内存泄漏
     */
    handleMemoryLeak(data) {
        console.warn('🚨 检测到内存泄漏:', data);
        
        // 1. 立即执行内存清理
        if (this.components.memoryManager) {
            this.components.memoryManager.performEmergencyCleanup();
        }
        
        // 2. 临时降低性能
        this.setPerformanceLevel('performance');
        
        // 3. 优先使用Worker
        this.preferWorkerCalculation = true;
        
        // 4. 通知用户
        if (this.components.pluginManager) {
            this.components.pluginManager.showNotification(
                '系统检测到内存压力，已自动优化性能设置',
                'warning',
                5000
            );
        }
    }
    
    /**
     * 设置性能级别
     */
    setPerformanceLevel(level) {
        this.systemStatus.performanceLevel = level;
        
        if (this.components.performanceManager) {
            // 根据级别调整性能设置
            switch (level) {
                case 'performance':
                    this.applyPerformanceMode();
                    break;
                case 'balanced':
                    this.applyBalancedMode();
                    break;
                case 'quality':
                    this.applyQualityMode();
                    break;
            }
        }
        
        // 触发性能级别变化事件
        if (window.EventBus) {
            window.EventBus.emit('system:performance-level-changed', {
                level: level,
                timestamp: Date.now()
            });
        }
        
        console.log(`📊 性能级别已设置为: ${level}`);
    }
    
    /**
     * 应用性能模式
     */
    applyPerformanceMode() {
        if (!this.viewer) return;
        
        const scene = this.viewer.scene;
        scene.globe.maximumScreenSpaceError = 8.0;
        scene.globe.tileCacheSize = 100;
        scene.fog.enabled = false;
        scene.skyAtmosphere.show = false;
        scene.requestRenderMode = true;
    }
    
    /**
     * 应用平衡模式
     */
    applyBalancedMode() {
        if (!this.viewer) return;
        
        const scene = this.viewer.scene;
        scene.globe.maximumScreenSpaceError = 4.0;
        scene.globe.tileCacheSize = 300;
        scene.fog.enabled = false;
        scene.skyAtmosphere.show = false;
        scene.requestRenderMode = true;
    }
    
    /**
     * 应用质量模式
     */
    applyQualityMode() {
        if (!this.viewer) return;
        
        const scene = this.viewer.scene;
        scene.globe.maximumScreenSpaceError = 2.0;
        scene.globe.tileCacheSize = 500;
        scene.fog.enabled = true;
        scene.skyAtmosphere.show = true;
        scene.requestRenderMode = false;
    }
    
    /**
     * 增强工具栏管理器
     */
    enhanceToolbarManager() {
        if (!this.components.toolbarManager) return;
        
        const originalActivateTool = this.components.toolbarManager.activateTool.bind(this.components.toolbarManager);
        
        this.components.toolbarManager.activateTool = async (toolId) => {
            // 工具激活前的系统检查
            const memoryInfo = await this.getMemoryStatus();
            
            if (memoryInfo && memoryInfo.usageRatio > 0.8) {
                console.warn('⚠️ 内存使用率过高，执行预防性清理');
                
                if (this.components.memoryManager) {
                    await this.components.memoryManager.performPreventiveCleanup();
                }
            }
            
            // 调用原始方法
            return await originalActivateTool(toolId);
        };
    }
    
    /**
     * 获取内存状态
     */
    async getMemoryStatus() {
        if (!this.components.memoryManager) return null;
        
        try {
            const memoryInfo = await this.components.memoryManager.getMemoryInfo();
            return {
                ...memoryInfo,
                usageRatio: memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit
            };
        } catch (error) {
            console.warn('获取内存状态失败:', error);
            return null;
        }
    }
    
    /**
     * 注册系统API
     */
    registerSystemAPI() {
        // 创建全局系统API
        window.SystemAPI = {
            // 性能控制
            setPerformanceLevel: (level) => this.setPerformanceLevel(level),
            getPerformanceLevel: () => this.systemStatus.performanceLevel,
            
            // 内存管理
            forceMemoryCleanup: () => {
                if (this.components.memoryManager) {
                    return this.components.memoryManager.performEmergencyCleanup();
                }
            },
            getMemoryStats: () => {
                if (this.components.memoryManager) {
                    return this.components.memoryManager.getMemoryStats();
                }
            },
            
            // Worker管理
            getWorkerStatus: () => {
                if (this.components.workerManager && typeof this.components.workerManager.getWorkerStatus === 'function') {
                    return this.components.workerManager.getWorkerStatus();
                }
                return null;
            },
            
            // 插件管理
            loadPlugin: (id, path, options) => {
                if (this.components.pluginManager) {
                    return this.components.pluginManager.loadPlugin(id, path, options);
                }
            },
            unloadPlugin: (id) => {
                if (this.components.pluginManager) {
                    return this.components.pluginManager.unloadPlugin(id);
                }
            },
            getPluginList: () => {
                if (this.components.pluginManager) {
                    return this.components.pluginManager.getPluginList();
                }
            },
            
            // 系统信息
            getSystemInfo: () => this.getSystemInfo(),
            getSystemMetrics: () => this.collectSystemMetrics()
        };
        
        console.log('🌐 系统API已注册');
    }
    
    /**
     * 获取系统信息
     */
    getSystemInfo() {
        return {
            status: this.systemStatus,
            components: {
                memoryManager: !!this.components.memoryManager,
                workerManager: !!this.components.workerManager,
                pluginManager: !!this.components.pluginManager,
                performanceManager: !!this.components.performanceManager,
                toolbarManager: !!this.components.toolbarManager
            },
            config: this.integrationConfig,
            version: '2.0.0',
            buildTime: new Date().toISOString()
        };
    }
    
    /**
     * 更新系统指标
     */
    updateSystemMetrics(category, data) {
        // 存储到系统指标中
        if (!this.systemMetrics) {
            this.systemMetrics = {};
        }
        
        this.systemMetrics[category] = {
            ...data,
            timestamp: Date.now()
        };
    }
    
    /**
     * 发送系统就绪事件
     */
    emitSystemReady() {
        const systemInfo = this.getSystemInfo();
        
        if (window.EventBus) {
            window.EventBus.emit('system:ready', systemInfo);
        }
        
        console.log('🎉 系统集成完成，所有组件已就绪!');
        console.log('📊 系统信息:', systemInfo);
    }
    
    /**
     * 销毁系统集成器
     */
    destroy() {
        // 销毁所有组件
        Object.values(this.components).forEach(component => {
            if (component && typeof component.destroy === 'function') {
                component.destroy();
            }
        });
        
        // 清理全局API
        if (window.SystemAPI) {
            delete window.SystemAPI;
        }
        
        if (window.GeometryAPI) {
            delete window.GeometryAPI;
        }
        
        console.log('🗑️ SystemIntegrator 已销毁');
    }
}

// 创建全局系统集成器
if (typeof window !== 'undefined') {
    window.SystemIntegrator = SystemIntegrator;
}

console.log('🔗 SystemIntegrator 系统集成器已加载完成');