/**
 * 🚀 Cesium性能管理器 - 智能性能优化和监控
 * 提供自适应性能控制、实时监控和优化建议
 */
class PerformanceManager {
    constructor(viewer) {
        this.viewer = viewer;
        this.isInteracting = false;
        this.renderModeTimer = null;
        this.performanceLevel = 'balanced'; // auto, performance, balanced, quality
        this.stats = {
            frameCount: 0,
            avgFPS: 0,
            memoryUsage: 0,
            tileCount: 0
        };
        
        this.init();
        console.log('🚀 PerformanceManager 性能管理器已启动');
    }

    /**
     * 初始化性能管理器
     */
    init() {
        this.setupSmartRendering();
        this.setupPerformanceMonitoring();
        this.setupAutoOptimization();
        this.logInitialStats();
    }

    /**
     * 智能渲染控制 - 交互时连续渲染，静止时按需渲染
     */
    setupSmartRendering() {
        // 相机移动开始
        this.viewer.scene.camera.moveStart.addEventListener(() => {
            this.isInteracting = true;
            this.viewer.scene.requestRenderMode = false;
            
            if (this.renderModeTimer) {
                clearTimeout(this.renderModeTimer);
            }
            
            console.log('🎮 交互开始 - 切换到连续渲染模式');
        });

        // 相机移动结束
        this.viewer.scene.camera.moveEnd.addEventListener(() => {
            this.renderModeTimer = setTimeout(() => {
                this.isInteracting = false;
                this.viewer.scene.requestRenderMode = true;
                this.viewer.scene.requestRender();
                console.log('⏸️  交互结束 - 切换到按需渲染模式');
            }, 500);
        });

        // 鼠标事件监听
        this.viewer.canvas.addEventListener('mousedown', () => {
            this.isInteracting = true;
        });

        this.viewer.canvas.addEventListener('mouseup', () => {
            setTimeout(() => {
                if (!this.isInteracting) {
                    this.viewer.scene.requestRenderMode = true;
                    this.viewer.scene.requestRender();
                }
            }, 300);
        });
    }

    /**
     * 性能监控
     */
    setupPerformanceMonitoring() {
        setInterval(() => {
            this.updateStats();
        }, 1000);

        // 瓦片加载监控
        this.viewer.scene.globe.tileLoadProgressEvent.addEventListener((queuedTileCount) => {
            this.stats.tileCount = queuedTileCount;
            
            // 如果瓦片加载过多，临时降低质量
            if (queuedTileCount > 20) {
                this.temporaryQualityReduction();
            }
        });
    }

    /**
     * 自动优化
     */
    setupAutoOptimization() {
        setInterval(() => {
            this.autoOptimize();
        }, 5000);
    }

    /**
     * 更新性能统计
     */
    updateStats() {
        const scene = this.viewer.scene;
        
        // 更新FPS（粗略估算）
        this.stats.frameCount++;
        
        // 估算内存使用（基于瓦片缓存）
        this.stats.memoryUsage = scene.globe.tileCacheSize * 0.5; // 每个瓦片约0.5MB
        
        // 记录当前配置
        this.stats.currentConfig = {
            renderMode: scene.requestRenderMode ? 'on-demand' : 'continuous',
            screenSpaceError: scene.globe.maximumScreenSpaceError,
            cacheSize: scene.globe.tileCacheSize,
            fogEnabled: scene.fog.enabled,
            atmosphereEnabled: scene.skyAtmosphere.show
        };
    }

    /**
     * 自动性能优化
     */
    autoOptimize() {
        const scene = this.viewer.scene;
        
        // 基于瓦片加载情况动态调整
        if (this.stats.tileCount > 15) {
            // 瓦片过多，降低精度
            if (scene.globe.maximumScreenSpaceError < 6.0) {
                scene.globe.maximumScreenSpaceError += 0.5;
                console.log(`📊 自动优化: 增加屏幕空间误差至 ${scene.globe.maximumScreenSpaceError}`);
            }
        } else if (this.stats.tileCount < 5) {
            // 瓦片较少，可以提高精度
            if (scene.globe.maximumScreenSpaceError > 3.0) {
                scene.globe.maximumScreenSpaceError -= 0.2;
                console.log(`📊 自动优化: 降低屏幕空间误差至 ${scene.globe.maximumScreenSpaceError}`);
            }
        }

        // 内存管理
        if (this.stats.memoryUsage > 200) { // 200MB
            this.cleanupMemory();
        }
    }

    /**
     * 临时降低质量（高负载时）
     */
    temporaryQualityReduction() {
        const scene = this.viewer.scene;
        
        if (!this._qualityReduced) {
            this._originalSSE = scene.globe.maximumScreenSpaceError;
            scene.globe.maximumScreenSpaceError = Math.max(6.0, this._originalSSE * 1.5);
            this._qualityReduced = true;
            
            console.log('⚡ 临时降低渲染质量以提升性能');
            
            // 10秒后恢复
            setTimeout(() => {
                if (this._qualityReduced) {
                    scene.globe.maximumScreenSpaceError = this._originalSSE;
                    this._qualityReduced = false;
                    console.log('✅ 恢复原始渲染质量');
                }
            }, 10000);
        }
    }

    /**
     * 内存清理
     */
    cleanupMemory() {
        const scene = this.viewer.scene;
        
        // 减少瓦片缓存
        const currentCache = scene.globe.tileCacheSize;
        scene.globe.tileCacheSize = Math.max(100, currentCache * 0.8);
        
        console.log(`🧹 内存清理: 瓦片缓存从 ${currentCache} 降至 ${scene.globe.tileCacheSize}`);
        
        // 强制垃圾回收（如果支持）
        if (window.gc) {
            window.gc();
        }
        
        this.viewer.scene.requestRender();
    }

    /**
     * 设置性能级别
     * @param {string} level - 'performance', 'balanced', 'quality'
     */
    setPerformanceLevel(level) {
        this.performanceLevel = level;
        const scene = this.viewer.scene;
        
        console.log(`🎛️  切换性能模式: ${level}`);
        
        switch (level) {
            case 'performance':
                // 高性能模式 - 牺牲视觉质量换取流畅度
                scene.globe.maximumScreenSpaceError = 6.0;
                scene.globe.tileCacheSize = 200;
                scene.fog.enabled = false;
                scene.skyAtmosphere.show = false;
                scene.globe.showGroundAtmosphere = false;
                console.log('⚡ 高性能模式已启用');
                break;
                
            case 'balanced':
                // 平衡模式 - 默认推荐设置
                scene.globe.maximumScreenSpaceError = 4.0;
                scene.globe.tileCacheSize = 300;
                scene.fog.enabled = false;
                scene.skyAtmosphere.show = false;
                scene.globe.showGroundAtmosphere = false;
                console.log('⚖️  平衡模式已启用');
                break;
                
            case 'quality':
                // 高质量模式 - 优先视觉效果
                scene.globe.maximumScreenSpaceError = 2.0;
                scene.globe.tileCacheSize = 500;
                scene.fog.enabled = true;
                scene.skyAtmosphere.show = true;
                scene.globe.showGroundAtmosphere = true;
                console.log('🎨 高质量模式已启用');
                break;
        }
        
        scene.requestRender();
    }

    /**
     * 获取当前性能统计
     */
    getStats() {
        return {
            ...this.stats,
            performanceLevel: this.performanceLevel,
            isInteracting: this.isInteracting,
            memoryMB: Math.round(this.stats.memoryUsage),
            qualityReduced: this._qualityReduced || false
        };
    }

    /**
     * 打印性能报告
     */
    printReport() {
        console.log('📊 ===== Cesium 性能报告 =====');
        console.table(this.getStats());
        
        const recommendations = this.getOptimizationRecommendations();
        if (recommendations.length > 0) {
            console.log('💡 优化建议:');
            recommendations.forEach((rec, index) => {
                console.log(`  ${index + 1}. ${rec}`);
            });
        }
    }

    /**
     * 获取优化建议
     */
    getOptimizationRecommendations() {
        const recommendations = [];
        const scene = this.viewer.scene;
        
        if (this.stats.tileCount > 20) {
            recommendations.push('瓦片加载过多，建议增加 maximumScreenSpaceError');
        }
        
        if (this.stats.memoryUsage > 300) {
            recommendations.push('内存占用较高，建议减少 tileCacheSize');
        }
        
        if (scene.fog.enabled || scene.skyAtmosphere.show) {
            recommendations.push('可关闭雾效和大气层效果以提升性能');
        }
        
        if (!scene.requestRenderMode) {
            recommendations.push('建议启用按需渲染模式');
        }
        
        return recommendations;
    }

    /**
     * 初始化日志
     */
    logInitialStats() {
        setTimeout(() => {
            console.log('📈 初始性能配置:');
            console.table({
                'Render Mode': this.viewer.scene.requestRenderMode ? '按需渲染 ✅' : '连续渲染 ⚠️',
                'Screen Space Error': this.viewer.scene.globe.maximumScreenSpaceError,
                'Tile Cache Size': this.viewer.scene.globe.tileCacheSize,
                'Fog Enabled': this.viewer.scene.fog.enabled ? '是' : '否 ✅',
                'Atmosphere': this.viewer.scene.skyAtmosphere.show ? '是' : '否 ✅',
                'Water Mask': this.viewer.scene.globe.terrainProvider.requestWaterMask ? '是' : '否 ✅'
            });
        }, 2000);
    }

    /**
     * 销毁性能管理器
     */
    destroy() {
        if (this.renderModeTimer) {
            clearTimeout(this.renderModeTimer);
        }
        console.log('🔄 PerformanceManager 已销毁');
    }
}

// 全局性能管理器
window.PerformanceManager = PerformanceManager;

// 便捷方法
window.setPerformanceLevel = (level) => {
    if (window.performanceManager) {
        window.performanceManager.setPerformanceLevel(level);
    }
};

window.getPerformanceStats = () => {
    if (window.performanceManager) {
        return window.performanceManager.getStats();
    }
};

window.printPerformanceReport = () => {
    if (window.performanceManager) {
        window.performanceManager.printReport();
    }
};

console.log('✅ PerformanceManager 类已加载，全局方法可用:');
console.log('  • setPerformanceLevel("performance|balanced|quality")');
console.log('  • getPerformanceStats()');
console.log('  • printPerformanceReport()');