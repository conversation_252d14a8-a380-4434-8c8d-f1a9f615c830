/**
 * 地形计算Web Worker - 将复杂地形计算移至后台线程
 * 解决主线程阻塞问题，提升用户体验
 */

// 导入Turf.js用于地理计算（需要在主线程预加载）
let turf = null;

// 消息处理器
self.onmessage = function(e) {
    const { type, data, requestId } = e.data;
    
    try {
        switch (type) {
            case 'INIT_TURF':
                // 初始化Turf.js
                turf = data.turf;
                postMessage({ type: 'INIT_COMPLETE', requestId });
                break;
                
            case 'CALCULATE_VOLUME':
                const volumeResult = calculateVolume(data);
                postMessage({ type: 'VOLUME_RESULT', data: volumeResult, requestId });
                break;
                
            case 'CALCULATE_PROFILE':
                const profileResult = calculateProfile(data);
                postMessage({ type: 'PROFILE_RESULT', data: profileResult, requestId });
                break;
                
            case 'PROCESS_GEOMETRY':
                const geometryResult = processGeometry(data);
                postMessage({ type: 'GEOMETRY_RESULT', data: geometryResult, requestId });
                break;
                
            case 'ANALYZE_TERRAIN':
                const terrainResult = analyzeTerrainData(data);
                postMessage({ type: 'TERRAIN_RESULT', data: terrainResult, requestId });
                break;
                
            default:
                throw new Error(`未知的任务类型: ${type}`);
        }
    } catch (error) {
        postMessage({ 
            type: 'ERROR', 
            error: error.message, 
            requestId 
        });
    }
};

/**
 * 地形开挖体积计算
 * @param {Object} data - 包含points(多边形点)和depth(开挖深度)
 * @returns {Object} 计算结果
 */
function calculateVolume(data) {
    const { points, depth, heightSamples } = data;
    
    if (!points || points.length < 3) {
        throw new Error('需要至少3个点来形成有效多边形');
    }
    
    // 转换为turf坐标格式
    const coordinates = points.map(point => [point.longitude, point.latitude]);
    coordinates.push(coordinates[0]); // 闭合多边形
    
    // 创建多边形
    const polygon = {
        type: 'Feature',
        geometry: {
            type: 'Polygon',
            coordinates: [coordinates]
        }
    };
    
    // 计算面积 (平方米)
    const area = calculatePolygonArea(polygon);
    
    // 计算体积 (考虑地形高度变化)
    let volume = 0;
    if (heightSamples && heightSamples.length > 0) {
        // 使用实际地形高度计算更精确的体积
        const avgHeight = heightSamples.reduce((sum, h) => sum + h, 0) / heightSamples.length;
        volume = area * (depth + avgHeight * 0.1); // 考虑地形起伏
    } else {
        // 简单体积计算
        volume = area * depth;
    }
    
    return {
        area: area,
        volume: volume,
        depth: depth,
        formattedArea: formatArea(area),
        formattedVolume: formatVolume(volume),
        calculationTime: Date.now()
    };
}

/**
 * 剖面分析计算
 * @param {Object} data - 包含startPoint, endPoint, samples等
 * @returns {Object} 剖面分析结果
 */
function calculateProfile(data) {
    const { startPoint, endPoint, heightSamples, sampleCount = 100 } = data;
    
    // 创建剖面线
    const line = {
        type: 'Feature',
        geometry: {
            type: 'LineString',
            coordinates: [
                [startPoint.longitude, startPoint.latitude],
                [endPoint.longitude, endPoint.latitude]
            ]
        }
    };
    
    // 计算总距离
    const totalDistance = calculateLineDistance(line);
    
    // 生成采样点
    const profilePoints = [];
    const step = 1 / (sampleCount - 1);
    
    for (let i = 0; i < sampleCount; i++) {
        const fraction = i * step;
        const distance = totalDistance * fraction;
        
        // 线性插值计算当前点坐标
        const longitude = lerp(startPoint.longitude, endPoint.longitude, fraction);
        const latitude = lerp(startPoint.latitude, endPoint.latitude, fraction);
        
        // 获取高度（如果提供了高度采样数据）
        let height = 0;
        if (heightSamples && heightSamples.length > i) {
            height = heightSamples[i];
        } else {
            // 简单的高度插值
            height = lerp(startPoint.height || 0, endPoint.height || 0, fraction);
        }
        
        profilePoints.push({
            distance: distance,
            height: height,
            longitude: longitude,
            latitude: latitude
        });
    }
    
    // 计算统计信息
    const heights = profilePoints.map(p => p.height);
    const statistics = {
        minHeight: Math.min(...heights),
        maxHeight: Math.max(...heights),
        avgHeight: heights.reduce((sum, h) => sum + h, 0) / heights.length,
        totalDistance: totalDistance,
        heightDifference: Math.max(...heights) - Math.min(...heights)
    };
    
    return {
        profilePoints: profilePoints,
        statistics: statistics,
        calculationTime: Date.now()
    };
}

/**
 * 几何数据处理
 * @param {Object} data - 几何数据
 * @returns {Object} 处理结果
 */
function processGeometry(data) {
    const { coordinates, operation } = data;
    
    let result = null;
    
    switch (operation) {
        case 'SIMPLIFY':
            result = simplifyGeometry(coordinates, data.tolerance || 0.001);
            break;
            
        case 'BUFFER':
            result = bufferGeometry(coordinates, data.distance || 100);
            break;
            
        case 'INTERSECTION':
            result = intersectGeometry(coordinates, data.otherGeometry);
            break;
            
        case 'CONVEX_HULL':
            result = convexHull(coordinates);
            break;
            
        default:
            throw new Error(`不支持的几何操作: ${operation}`);
    }
    
    return {
        result: result,
        operation: operation,
        inputPointCount: coordinates.length,
        outputPointCount: result ? result.length : 0,
        calculationTime: Date.now()
    };
}

/**
 * 地形数据分析
 * @param {Object} data - 地形数据
 * @returns {Object} 分析结果
 */
function analyzeTerrainData(data) {
    const { elevationData, bounds, analysisType } = data;
    
    let result = {};
    
    switch (analysisType) {
        case 'SLOPE_ANALYSIS':
            result = calculateSlope(elevationData, bounds);
            break;
            
        case 'ASPECT_ANALYSIS':
            result = calculateAspect(elevationData, bounds);
            break;
            
        case 'WATERSHED':
            result = calculateWatershed(elevationData, bounds);
            break;
            
        case 'VISIBILITY':
            result = calculateVisibility(elevationData, bounds, data.viewPoint);
            break;
            
        default:
            throw new Error(`不支持的地形分析类型: ${analysisType}`);
    }
    
    return {
        result: result,
        analysisType: analysisType,
        calculationTime: Date.now()
    };
}

// ============= 辅助函数 =============

/**
 * 计算多边形面积（哈弗辛公式）
 */
function calculatePolygonArea(polygon) {
    const coordinates = polygon.geometry.coordinates[0];
    let area = 0;
    const R = 6371000; // 地球半径（米）
    
    for (let i = 0; i < coordinates.length - 1; i++) {
        const [lon1, lat1] = coordinates[i];
        const [lon2, lat2] = coordinates[i + 1];
        
        const dLat = toRadians(lat2 - lat1);
        const dLon = toRadians(lon2 - lon1);
        
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
                Math.sin(dLon/2) * Math.sin(dLon/2);
        
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        area += R * c * R * Math.cos(toRadians((lat1 + lat2) / 2)) * toRadians(Math.abs(lon2 - lon1));
    }
    
    return Math.abs(area);
}

/**
 * 计算线段距离
 */
function calculateLineDistance(line) {
    const coordinates = line.geometry.coordinates;
    let distance = 0;
    
    for (let i = 0; i < coordinates.length - 1; i++) {
        const [lon1, lat1] = coordinates[i];
        const [lon2, lat2] = coordinates[i + 1];
        distance += haversineDistance(lat1, lon1, lat2, lon2);
    }
    
    return distance;
}

/**
 * 哈弗辛距离计算
 */
function haversineDistance(lat1, lon1, lat2, lon2) {
    const R = 6371000; // 地球半径（米）
    const dLat = toRadians(lat2 - lat1);
    const dLon = toRadians(lon2 - lon1);
    
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
            Math.sin(dLon/2) * Math.sin(dLon/2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
}

/**
 * 线性插值
 */
function lerp(start, end, fraction) {
    return start + (end - start) * fraction;
}

/**
 * 角度转弧度
 */
function toRadians(degrees) {
    return degrees * (Math.PI / 180);
}

/**
 * 格式化面积显示
 */
function formatArea(area) {
    if (area >= 1000000) {
        return (area / 1000000).toFixed(2) + ' km²';
    } else {
        return area.toFixed(2) + ' m²';
    }
}

/**
 * 格式化体积显示
 */
function formatVolume(volume) {
    if (volume >= 1000000) {
        return (volume / 1000000).toFixed(2) + ' 百万m³';
    } else if (volume >= 1000) {
        return (volume / 1000).toFixed(2) + ' 千m³';
    } else {
        return volume.toFixed(2) + ' m³';
    }
}

/**
 * 几何简化
 */
function simplifyGeometry(coordinates, tolerance) {
    // Douglas-Peucker算法简化
    if (coordinates.length <= 2) return coordinates;
    
    // 找到距离起点终点连线最远的点
    let maxDistance = 0;
    let maxIndex = 0;
    
    const start = coordinates[0];
    const end = coordinates[coordinates.length - 1];
    
    for (let i = 1; i < coordinates.length - 1; i++) {
        const distance = pointToLineDistance(coordinates[i], start, end);
        if (distance > maxDistance) {
            maxDistance = distance;
            maxIndex = i;
        }
    }
    
    // 如果最大距离小于容差，简化为直线
    if (maxDistance < tolerance) {
        return [start, end];
    }
    
    // 递归简化
    const left = simplifyGeometry(coordinates.slice(0, maxIndex + 1), tolerance);
    const right = simplifyGeometry(coordinates.slice(maxIndex), tolerance);
    
    return left.slice(0, -1).concat(right);
}

/**
 * 点到直线距离
 */
function pointToLineDistance(point, lineStart, lineEnd) {
    const [x, y] = point;
    const [x1, y1] = lineStart;
    const [x2, y2] = lineEnd;
    
    const A = x - x1;
    const B = y - y1;
    const C = x2 - x1;
    const D = y2 - y1;
    
    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    
    if (lenSq === 0) return Math.sqrt(A * A + B * B);
    
    const param = dot / lenSq;
    
    let xx, yy;
    if (param < 0) {
        xx = x1;
        yy = y1;
    } else if (param > 1) {
        xx = x2;
        yy = y2;
    } else {
        xx = x1 + param * C;
        yy = y1 + param * D;
    }
    
    const dx = x - xx;
    const dy = y - yy;
    return Math.sqrt(dx * dx + dy * dy);
}

/**
 * 缓冲区生成
 */
function bufferGeometry(coordinates, distance) {
    // 简化的缓冲区算法
    const buffered = [];
    const distanceRad = distance / 111319.9; // 大致的度数转换
    
    coordinates.forEach(coord => {
        // 在每个点周围生成圆形缓冲区的采样点
        for (let i = 0; i < 16; i++) {
            const angle = (i / 16) * 2 * Math.PI;
            const newLon = coord[0] + distanceRad * Math.cos(angle);
            const newLat = coord[1] + distanceRad * Math.sin(angle);
            buffered.push([newLon, newLat]);
        }
    });
    
    return buffered;
}

/**
 * 凸包计算（Graham扫描法）
 */
function convexHull(points) {
    if (points.length < 3) return points;
    
    // 找到最下方的点
    let bottom = 0;
    for (let i = 1; i < points.length; i++) {
        if (points[i][1] < points[bottom][1] || 
            (points[i][1] === points[bottom][1] && points[i][0] < points[bottom][0])) {
            bottom = i;
        }
    }
    
    // 将最下方的点放到首位
    [points[0], points[bottom]] = [points[bottom], points[0]];
    
    // 按极角排序
    const pivot = points[0];
    points.slice(1).sort((a, b) => {
        const angleA = Math.atan2(a[1] - pivot[1], a[0] - pivot[0]);
        const angleB = Math.atan2(b[1] - pivot[1], b[0] - pivot[0]);
        return angleA - angleB;
    });
    
    // Graham扫描
    const hull = [points[0], points[1]];
    
    for (let i = 2; i < points.length; i++) {
        while (hull.length > 1 && 
               crossProduct(hull[hull.length-2], hull[hull.length-1], points[i]) <= 0) {
            hull.pop();
        }
        hull.push(points[i]);
    }
    
    return hull;
}

/**
 * 向量叉积
 */
function crossProduct(o, a, b) {
    return (a[0] - o[0]) * (b[1] - o[1]) - (a[1] - o[1]) * (b[0] - o[0]);
}

/**
 * 坡度计算
 */
function calculateSlope(elevationData, bounds) {
    // 简化的坡度计算
    const slopes = [];
    const { width, height } = bounds;
    
    for (let y = 1; y < height - 1; y++) {
        for (let x = 1; x < width - 1; x++) {
            const dz_dx = (elevationData[y * width + x + 1] - elevationData[y * width + x - 1]) / 2;
            const dz_dy = (elevationData[(y + 1) * width + x] - elevationData[(y - 1) * width + x]) / 2;
            
            const slope = Math.atan(Math.sqrt(dz_dx * dz_dx + dz_dy * dz_dy)) * 180 / Math.PI;
            slopes.push(slope);
        }
    }
    
    return slopes;
}

/**
 * 坡向计算
 */
function calculateAspect(elevationData, bounds) {
    const aspects = [];
    const { width, height } = bounds;
    
    for (let y = 1; y < height - 1; y++) {
        for (let x = 1; x < width - 1; x++) {
            const dz_dx = (elevationData[y * width + x + 1] - elevationData[y * width + x - 1]) / 2;
            const dz_dy = (elevationData[(y + 1) * width + x] - elevationData[(y - 1) * width + x]) / 2;
            
            let aspect = Math.atan2(dz_dy, -dz_dx) * 180 / Math.PI;
            if (aspect < 0) aspect += 360;
            
            aspects.push(aspect);
        }
    }
    
    return aspects;
}

/**
 * 简化的流域分析
 */
function calculateWatershed(elevationData, bounds) {
    // 这里返回简化的流域结果
    return {
        watershedBoundaries: [],
        flowDirections: [],
        message: '流域分析需要更复杂的算法实现'
    };
}

/**
 * 可视性分析
 */
function calculateVisibility(elevationData, bounds, viewPoint) {
    // 简化的视线分析
    return {
        visibleAreas: [],
        hiddenAreas: [],
        viewshed: [],
        message: '可视性分析需要更复杂的算法实现'
    };
}

console.log('🔧 Terrain Web Worker 已初始化完成');