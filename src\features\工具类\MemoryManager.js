/**
 * 智能内存管理器 - 实现自动垃圾回收和长时间运行稳定性
 * 监控内存使用情况并自动优化，防止内存泄漏
 */
class MemoryManager {
    constructor(viewer) {
        this.viewer = viewer;
        this.memoryThresholds = {
            warning: 100 * 1024 * 1024,    // 100MB 警告阈值
            critical: 200 * 1024 * 1024,   // 200MB 紧急阈值
            cleanup: 150 * 1024 * 1024     // 150MB 清理阈值
        };
        
        // 内存监控状态
        this.monitoring = {
            enabled: true,
            interval: 30000,  // 30秒检查一次
            lastCheck: Date.now(),
            stats: {
                peakUsage: 0,
                cleanupCount: 0,
                leakWarnings: 0
            }
        };
        
        // 资源追踪
        this.trackedResources = {
            entities: new WeakMap(),
            eventListeners: new Set(),
            timers: new Set(),
            workers: new Set(),
            textures: new WeakMap(),
            geometries: new WeakMap(),
            materials: new WeakMap()
        };
        
        // 自动清理规则
        this.cleanupRules = new Map();
        
        this.initializeMemoryManagement();
        console.log('🧠 MemoryManager 智能内存管理器初始化完成');
    }
    
    /**
     * 初始化内存管理
     */
    initializeMemoryManagement() {
        // 设置定期内存检查
        this.startMemoryMonitoring();
        
        // 设置实体生命周期管理
        this.setupEntityLifecycleManagement();
        
        // 设置事件监听器追踪
        this.setupEventListenerTracking();
        
        // 设置Cesium特定的内存管理
        this.setupCesiumMemoryManagement();
        
        // 注册清理规则
        this.registerCleanupRules();
        
        // 监听页面可见性变化
        this.setupVisibilityChangeHandling();
        
        // 设置内存压力监听
        this.setupMemoryPressureHandling();
    }
    
    /**
     * 开始内存监控
     */
    startMemoryMonitoring() {
        if (!this.monitoring.enabled) return;
        
        this.monitoringTimer = setInterval(() => {
            this.performMemoryCheck();
        }, this.monitoring.interval);
        
        // 立即执行一次检查
        this.performMemoryCheck();
        
        console.log(`📊 内存监控已启动，检查间隔: ${this.monitoring.interval / 1000}秒`);
    }
    
    /**
     * 执行内存检查
     */
    async performMemoryCheck() {
        try {
            const memoryInfo = await this.getMemoryInfo();
            this.monitoring.lastCheck = Date.now();
            
            // 更新峰值使用记录
            if (memoryInfo.usedJSHeapSize > this.monitoring.stats.peakUsage) {
                this.monitoring.stats.peakUsage = memoryInfo.usedJSHeapSize;
            }
            
            // 检查内存阈值
            if (memoryInfo.usedJSHeapSize > this.memoryThresholds.critical) {
                console.warn('🚨 内存使用达到紧急阈值，执行强制清理');
                await this.performEmergencyCleanup();
            } else if (memoryInfo.usedJSHeapSize > this.memoryThresholds.warning) {
                console.warn('⚠️ 内存使用超过警告阈值，执行预防性清理');
                await this.performPreventiveCleanup();
            }
            
            // 触发内存状态事件
            this.emitMemoryStatus(memoryInfo);
            
        } catch (error) {
            console.error('内存检查失败:', error);
        }
    }
    
    /**
     * 获取内存信息
     */
    async getMemoryInfo() {
        if (performance.memory) {
            return {
                usedJSHeapSize: performance.memory.usedJSHeapSize,
                totalJSHeapSize: performance.memory.totalJSHeapSize,
                jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
                timestamp: Date.now()
            };
        } else {
            // 浏览器不支持performance.memory时的估算
            const estimate = await this.estimateMemoryUsage();
            return {
                usedJSHeapSize: estimate,
                totalJSHeapSize: estimate * 1.2,
                jsHeapSizeLimit: estimate * 4,
                timestamp: Date.now(),
                estimated: true
            };
        }
    }
    
    /**
     * 估算内存使用量
     */
    async estimateMemoryUsage() {
        let estimated = 0;
        
        // 估算实体内存使用
        if (this.viewer && this.viewer.entities) {
            estimated += this.viewer.entities.values.length * 10000; // 每个实体约10KB
        }
        
        // 估算纹理内存使用
        if (this.viewer && this.viewer.scene) {
            estimated += this.viewer.scene.globe.tileCacheSize * 50000; // 每个瓦片约50KB
        }
        
        // 基础内存估算
        estimated += 50 * 1024 * 1024; // 基础50MB
        
        return estimated;
    }
    
    /**
     * 执行紧急清理
     */
    async performEmergencyCleanup() {
        console.log('🧹 开始紧急内存清理...');
        const startTime = Date.now();
        
        // 1. 清理过期实体
        await this.cleanupExpiredEntities(true);
        
        // 2. 强制垃圾回收（如果支持）
        this.forceGarbageCollection();
        
        // 3. 清理Cesium缓存
        this.cleanupCesiumCaches(true);
        
        // 4. 清理事件历史
        this.cleanupEventHistory(0.1); // 只保留10%的历史
        
        // 5. 清理临时数据
        this.cleanupTemporaryData();
        
        // 6. 降低渲染质量
        this.reduceCesiumQuality();
        
        const endTime = Date.now();
        this.monitoring.stats.cleanupCount++;
        
        console.log(`✅ 紧急清理完成，耗时: ${endTime - startTime}ms`);
        
        // 延迟后检查清理效果
        setTimeout(() => {
            this.verifyCleanupEffectiveness();
        }, 2000);
    }
    
    /**
     * 执行预防性清理
     */
    async performPreventiveCleanup() {
        console.log('🧹 开始预防性内存清理...');
        
        // 1. 清理过期实体
        await this.cleanupExpiredEntities(false);
        
        // 2. 清理事件历史
        this.cleanupEventHistory(0.5); // 保留50%的历史
        
        // 3. 优化Cesium缓存
        this.optimizeCesiumCaches();
        
        // 4. 清理未使用的资源
        this.cleanupUnusedResources();
        
        console.log('✅ 预防性清理完成');
    }
    
    /**
     * 清理过期实体
     */
    async cleanupExpiredEntities(aggressive = false) {
        if (!this.viewer || !this.viewer.entities) return;
        
        const entities = this.viewer.entities.values;
        const now = Date.now();
        const toRemove = [];
        
        for (let i = entities.length - 1; i >= 0; i--) {
            const entity = entities[i];
            
            // 检查是否为临时实体
            if (entity._temporaryObject) {
                const age = now - (entity._createTime || 0);
                const maxAge = aggressive ? 60000 : 300000; // 激进模式1分钟，正常模式5分钟
                
                if (age > maxAge) {
                    toRemove.push(entity);
                }
            }
            
            // 检查是否为隐藏实体
            if (aggressive && entity.show === false) {
                const hiddenTime = now - (entity._hideTime || 0);
                if (hiddenTime > 120000) { // 隐藏超过2分钟
                    toRemove.push(entity);
                }
            }
        }
        
        // 批量移除实体
        if (toRemove.length > 0) {
            toRemove.forEach(entity => {
                try {
                    this.viewer.entities.remove(entity);
                } catch (e) {
                    console.warn('移除实体失败:', e);
                }
            });
            
            console.log(`🗑️ 清理了 ${toRemove.length} 个过期实体`);
        }
    }
    
    /**
     * 强制垃圾回收
     */
    forceGarbageCollection() {
        // 尝试触发垃圾回收（仅在开发环境或支持的浏览器中）
        if (window.gc && typeof window.gc === 'function') {
            try {
                window.gc();
                console.log('🗑️ 强制垃圾回收执行完成');
            } catch (e) {
                console.warn('强制垃圾回收失败:', e);
            }
        } else {
            // 创建大量临时对象来触发GC
            const temp = [];
            for (let i = 0; i < 100000; i++) {
                temp.push(new Array(100));
            }
            temp.length = 0;
        }
    }
    
    /**
     * 清理Cesium缓存
     */
    cleanupCesiumCaches(aggressive = false) {
        if (!this.viewer) return;
        
        try {
            // 清理瓦片缓存
            if (this.viewer.scene && this.viewer.scene.globe) {
                const originalCacheSize = this.viewer.scene.globe.tileCacheSize;
                const newCacheSize = aggressive ? 100 : Math.floor(originalCacheSize * 0.7);
                this.viewer.scene.globe.tileCacheSize = newCacheSize;
                
                // 立即恢复正常缓存大小（触发清理）
                setTimeout(() => {
                    this.viewer.scene.globe.tileCacheSize = originalCacheSize;
                }, 1000);
            }
            
            // 清理图像缓存
            if (this.viewer.scene && this.viewer.scene.context) {
                const context = this.viewer.scene.context;
                if (context._textureCache) {
                    const cacheSize = Object.keys(context._textureCache).length;
                    if (aggressive && cacheSize > 50) {
                        // 清理一半的纹理缓存
                        const keys = Object.keys(context._textureCache);
                        keys.slice(0, Math.floor(keys.length / 2)).forEach(key => {
                            delete context._textureCache[key];
                        });
                        console.log(`🗑️ 清理了 ${Math.floor(keys.length / 2)} 个纹理缓存`);
                    }
                }
            }
            
        } catch (error) {
            console.warn('清理Cesium缓存时出错:', error);
        }
    }
    
    /**
     * 清理事件历史
     */
    cleanupEventHistory(keepRatio = 0.5) {
        if (window.EventBus && window.EventBus.eventHistory) {
            const history = window.EventBus.eventHistory;
            const originalLength = history.length;
            const keepCount = Math.floor(originalLength * keepRatio);
            
            if (originalLength > 100 && keepCount < originalLength) {
                // 保留最新的事件
                window.EventBus.eventHistory = history.slice(-keepCount);
                console.log(`🗑️ 清理了 ${originalLength - keepCount} 条事件历史记录`);
            }
        }
    }
    
    /**
     * 清理临时数据
     */
    cleanupTemporaryData() {
        // 清理localStorage中的临时数据
        const tempKeys = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && (key.startsWith('temp_') || key.startsWith('cache_'))) {
                tempKeys.push(key);
            }
        }
        
        tempKeys.forEach(key => {
            try {
                localStorage.removeItem(key);
            } catch (e) {
                console.warn(`清理localStorage项目 ${key} 失败:`, e);
            }
        });
        
        if (tempKeys.length > 0) {
            console.log(`🗑️ 清理了 ${tempKeys.length} 个临时localStorage项目`);
        }
        
        // 清理sessionStorage
        try {
            sessionStorage.clear();
            console.log('🗑️ 清理了sessionStorage');
        } catch (e) {
            console.warn('清理sessionStorage失败:', e);
        }
    }
    
    /**
     * 降低Cesium渲染质量
     */
    reduceCesiumQuality() {
        if (!this.viewer || !this.viewer.scene) return;
        
        const scene = this.viewer.scene;
        
        // 临时降低渲染质量
        const originalValues = {
            maximumScreenSpaceError: scene.globe.maximumScreenSpaceError,
            tileCacheSize: scene.globe.tileCacheSize,
            fog: scene.fog.enabled,
            atmosphere: scene.skyAtmosphere.show
        };
        
        // 应用低质量设置
        scene.globe.maximumScreenSpaceError = Math.min(8.0, originalValues.maximumScreenSpaceError * 2);
        scene.globe.tileCacheSize = Math.max(50, Math.floor(originalValues.tileCacheSize * 0.5));
        scene.fog.enabled = false;
        scene.skyAtmosphere.show = false;
        
        console.log('📉 临时降低了渲染质量以释放内存');
        
        // 5分钟后恢复正常质量
        setTimeout(() => {
            scene.globe.maximumScreenSpaceError = originalValues.maximumScreenSpaceError;
            scene.globe.tileCacheSize = originalValues.tileCacheSize;
            scene.fog.enabled = originalValues.fog;
            scene.skyAtmosphere.show = originalValues.atmosphere;
            console.log('📈 已恢复正常渲染质量');
        }, 300000);
    }
    
    /**
     * 优化Cesium缓存
     */
    optimizeCesiumCaches() {
        if (!this.viewer) return;
        
        // 适度清理缓存
        this.cleanupCesiumCaches(false);
        
        // 优化瓦片请求
        if (this.viewer.scene && this.viewer.scene.globe) {
            const globe = this.viewer.scene.globe;
            globe.preloadAncestors = false;
            globe.preloadSiblings = false;
            globe.loadingDescendantLimit = Math.min(10, globe.loadingDescendantLimit);
        }
    }
    
    /**
     * 清理未使用的资源
     */
    cleanupUnusedResources() {
        // 清理未使用的计时器
        this.trackedResources.timers.forEach(timer => {
            if (timer && timer.completed) {
                clearTimeout(timer.id);
                this.trackedResources.timers.delete(timer);
            }
        });
        
        // 清理断开的事件监听器
        this.trackedResources.eventListeners.forEach(listener => {
            if (listener && listener.isDisconnected) {
                this.trackedResources.eventListeners.delete(listener);
            }
        });
    }
    
    /**
     * 验证清理效果
     */
    async verifyCleanupEffectiveness() {
        const memoryInfo = await this.getMemoryInfo();
        
        if (memoryInfo.usedJSHeapSize > this.memoryThresholds.critical) {
            this.monitoring.stats.leakWarnings++;
            console.error('🚨 内存清理效果不佳，可能存在内存泄漏');
            
            // 触发内存泄漏警告事件
            if (window.EventBus) {
                window.EventBus.emit('system:memory:leak-warning', {
                    currentUsage: memoryInfo.usedJSHeapSize,
                    threshold: this.memoryThresholds.critical,
                    cleanupCount: this.monitoring.stats.cleanupCount
                });
            }
        } else {
            console.log('✅ 内存清理效果良好');
        }
    }
    
    /**
     * 设置实体生命周期管理
     */
    setupEntityLifecycleManagement() {
        if (!this.viewer) return;
        
        // 重写entities.add方法来追踪实体
        const originalAdd = this.viewer.entities.add.bind(this.viewer.entities);
        this.viewer.entities.add = (entity) => {
            // 添加生命周期信息
            entity._createTime = Date.now();
            entity._isTracked = true;
            
            // 如果是临时对象，设置标记
            if (entity.isTemporary || entity._temporaryObject) {
                entity._temporaryObject = true;
            }
            
            return originalAdd(entity);
        };
        
        console.log('📝 实体生命周期管理已设置');
    }
    
    /**
     * 设置事件监听器追踪
     */
    setupEventListenerTracking() {
        // 重写addEventListener来追踪事件监听器
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        const memoryManager = this;
        
        EventTarget.prototype.addEventListener = function(type, listener, options) {
            // 追踪事件监听器
            memoryManager.trackedResources.eventListeners.add({
                target: this,
                type: type,
                listener: listener,
                addTime: Date.now()
            });
            
            return originalAddEventListener.call(this, type, listener, options);
        };
        
        console.log('📝 事件监听器追踪已设置');
    }
    
    /**
     * 设置Cesium特定的内存管理
     */
    setupCesiumMemoryManagement() {
        if (!this.viewer) return;
        
        // 监听场景变化
        if (this.viewer.scene) {
            this.viewer.scene.camera.moveEnd.addEventListener(() => {
                // 相机移动结束后清理不可见的实体
                setTimeout(() => {
                    this.cleanupOffscreenEntities();
                }, 5000);
            });
        }
        
        console.log('📝 Cesium内存管理已设置');
    }
    
    /**
     * 清理屏幕外的实体
     */
    cleanupOffscreenEntities() {
        if (!this.viewer || !this.viewer.entities) return;
        
        const camera = this.viewer.camera;
        const scene = this.viewer.scene;
        const entities = this.viewer.entities.values;
        let cleanedCount = 0;
        
        for (let i = entities.length - 1; i >= 0; i--) {
            const entity = entities[i];
            
            if (entity._temporaryObject && entity.position) {
                try {
                    const position = entity.position.getValue(this.viewer.clock.currentTime);
                    if (position) {
                        // 检查实体是否在视锥体外
                        const frustum = camera.frustum;
                        const cullingVolume = frustum.computeCullingVolume(
                            camera.position,
                            camera.direction,
                            camera.up
                        );
                        
                        const boundingSphere = new Cesium.BoundingSphere(position, 100);
                        const visibility = cullingVolume.computeVisibility(boundingSphere);
                        
                        if (visibility === Cesium.Intersect.OUTSIDE) {
                            const age = Date.now() - (entity._createTime || 0);
                            if (age > 600000) { // 10分钟
                                this.viewer.entities.remove(entity);
                                cleanedCount++;
                            }
                        }
                    }
                } catch (e) {
                    // 忽略计算错误
                }
            }
        }
        
        if (cleanedCount > 0) {
            console.log(`🗑️ 清理了 ${cleanedCount} 个屏幕外实体`);
        }
    }
    
    /**
     * 注册清理规则
     */
    registerCleanupRules() {
        // 实体清理规则
        this.cleanupRules.set('entities', {
            maxAge: 300000,     // 5分钟
            maxCount: 1000,     // 最大1000个实体
            priority: 1
        });
        
        // 事件历史清理规则
        this.cleanupRules.set('eventHistory', {
            maxAge: 600000,     // 10分钟
            maxCount: 500,      // 最大500条历史
            priority: 2
        });
        
        // 纹理缓存清理规则
        this.cleanupRules.set('textureCache', {
            maxAge: 1800000,    // 30分钟
            maxCount: 100,      // 最大100个纹理
            priority: 3
        });
        
        console.log('📋 内存清理规则已注册');
    }
    
    /**
     * 设置页面可见性变化处理
     */
    setupVisibilityChangeHandling() {
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // 页面隐藏时执行清理
                console.log('📴 页面隐藏，执行内存清理');
                this.performPreventiveCleanup();
            } else {
                // 页面恢复可见时重新开始监控
                console.log('👁️ 页面恢复可见，重新开始内存监控');
                this.performMemoryCheck();
            }
        });
        
        console.log('📝 页面可见性变化处理已设置');
    }
    
    /**
     * 设置内存压力处理
     */
    setupMemoryPressureHandling() {
        // 监听内存压力事件（如果支持）
        if ('memory' in navigator && 'addEventListener' in navigator.memory) {
            navigator.memory.addEventListener('pressure', (event) => {
                console.warn('🚨 检测到内存压力:', event.level);
                
                switch (event.level) {
                    case 'moderate':
                        this.performPreventiveCleanup();
                        break;
                    case 'critical':
                        this.performEmergencyCleanup();
                        break;
                }
            });
        }
        
        // 设置内存使用监控阈值
        if (performance.memory) {
            const memLimit = performance.memory.jsHeapSizeLimit;
            this.memoryThresholds.warning = memLimit * 0.7;   // 70%
            this.memoryThresholds.critical = memLimit * 0.85; // 85%
            this.memoryThresholds.cleanup = memLimit * 0.8;   // 80%
        }
        
        console.log('📝 内存压力处理已设置');
    }
    
    /**
     * 发送内存状态事件
     */
    emitMemoryStatus(memoryInfo) {
        if (window.EventBus) {
            window.EventBus.emit('system:memory:status', {
                ...memoryInfo,
                thresholds: this.memoryThresholds,
                stats: this.monitoring.stats
            });
        }
    }
    
    /**
     * 获取内存统计
     */
    getMemoryStats() {
        return {
            monitoring: this.monitoring,
            thresholds: this.memoryThresholds,
            trackedResources: {
                eventListeners: this.trackedResources.eventListeners.size,
                timers: this.trackedResources.timers.size,
                workers: this.trackedResources.workers.size
            }
        };
    }
    
    /**
     * 停止内存监控
     */
    stopMemoryMonitoring() {
        if (this.monitoringTimer) {
            clearInterval(this.monitoringTimer);
            this.monitoringTimer = null;
        }
        
        this.monitoring.enabled = false;
        console.log('⏹️ 内存监控已停止');
    }
    
    /**
     * 销毁内存管理器
     */
    destroy() {
        this.stopMemoryMonitoring();
        
        // 清理追踪的资源
        this.trackedResources.timers.forEach(timer => {
            clearTimeout(timer.id);
        });
        
        this.trackedResources.workers.forEach(worker => {
            worker.terminate();
        });
        
        // 清理事件监听器
        this.trackedResources.eventListeners.clear();
        this.trackedResources.timers.clear();
        this.trackedResources.workers.clear();
        
        console.log('🗑️ MemoryManager 已销毁');
    }
}

// 创建全局内存管理器实例
if (typeof window !== 'undefined') {
    window.MemoryManager = MemoryManager;
}

console.log('🧠 MemoryManager 智能内存管理器已加载完成');