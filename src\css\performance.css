/* 🚀 性能优化相关样式 */

/* 性能状态指示器 */
.performance-indicator {
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-family: 'Courier New', monospace;
    z-index: 10000;
    display: none;
    transition: all 0.3s ease;
}

.performance-indicator.show {
    display: block;
}

.performance-indicator.performance-mode {
    background: rgba(255, 87, 87, 0.9);
    box-shadow: 0 0 10px rgba(255, 87, 87, 0.3);
}

.performance-indicator.balanced-mode {
    background: rgba(52, 168, 83, 0.9);
    box-shadow: 0 0 10px rgba(52, 168, 83, 0.3);
}

.performance-indicator.quality-mode {
    background: rgba(66, 133, 244, 0.9);
    box-shadow: 0 0 10px rgba(66, 133, 244, 0.3);
}

/* 性能控制面板 */
.performance-controls {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    font-size: 14px;
    display: none;
    z-index: 9999;
}

.performance-controls.show {
    display: block;
    animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.performance-controls h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 16px;
}

.performance-mode-btn {
    display: block;
    width: 100%;
    margin: 5px 0;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s ease;
    text-align: left;
}

.performance-mode-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.performance-mode-btn.performance {
    background: #ff5757;
    color: white;
}

.performance-mode-btn.balanced {
    background: #34a853;
    color: white;
}

.performance-mode-btn.quality {
    background: #4285f4;
    color: white;
}

.performance-mode-btn.active {
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.4);
    transform: scale(1.02);
}

/* 性能统计显示 */
.performance-stats {
    font-size: 11px;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #eee;
    color: #666;
}

.performance-stats .stat-item {
    display: flex;
    justify-content: space-between;
    margin: 3px 0;
}

.performance-stats .stat-value {
    font-weight: bold;
}

/* 性能优化提示 */
.performance-tip {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 20px 30px;
    border-radius: 10px;
    text-align: center;
    z-index: 11000;
    display: none;
    max-width: 400px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.performance-tip.show {
    display: block;
    animation: fadeInScale 0.4s ease;
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.performance-tip h3 {
    margin: 0 0 15px 0;
    color: #4CAF50;
    font-size: 18px;
}

.performance-tip p {
    margin: 10px 0;
    line-height: 1.5;
    font-size: 14px;
}

.performance-tip .tip-icon {
    font-size: 32px;
    margin-bottom: 15px;
}

/* 性能警告 */
.performance-warning {
    background: rgba(255, 193, 7, 0.9);
    color: #333;
}

.performance-warning .tip-icon {
    color: #ff9800;
}

/* 渲染模式切换动画 */
.cesium-viewer {
    transition: filter 0.3s ease;
}

.cesium-viewer.performance-mode {
    /* 高性能模式时可以添加一些视觉提示 */
}

.cesium-viewer.quality-mode {
    /* 高质量模式时的视觉效果 */
}

/* 瓦片加载指示器 */
.tile-loading-indicator {
    position: fixed;
    top: 50px;
    right: 10px;
    background: rgba(33, 150, 243, 0.9);
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    display: none;
    z-index: 9998;
}

.tile-loading-indicator.show {
    display: block;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* 内存使用指示器 */
.memory-indicator {
    position: fixed;
    top: 80px;
    right: 10px;
    background: rgba(156, 39, 176, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    z-index: 9998;
}

.memory-indicator.high {
    background: rgba(244, 67, 54, 0.9);
    animation: shake 0.5s infinite;
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-2px);
    }
    75% {
        transform: translateX(2px);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .performance-controls {
        bottom: 10px;
        right: 10px;
        left: 10px;
        width: auto;
        min-width: auto;
    }
    
    .performance-tip {
        left: 10px;
        right: 10px;
        width: auto;
        max-width: none;
        transform: translateY(-50%);
    }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    .performance-controls {
        background: rgba(33, 33, 33, 0.95);
        color: white;
    }
    
    .performance-controls h4 {
        color: #fff;
    }
    
    .performance-stats {
        border-top-color: #555;
        color: #ccc;
    }
}